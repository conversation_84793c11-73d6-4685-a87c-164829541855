# API Gateway - External API Documentation

## Overview
API Gateway menyediakan akses terpusat ke semua microservices dalam sistem ATMA (AI-Driven Talent Mapping Assessment). Gateway ini menangani routing, autentikasi, rate limiting, dan proxy ke berbagai services.

**Gateway Information:**
- **Service Name:** api-gateway
- **Port:** 3000
- **Base URL:** `http://localhost:3000/api/`
- **Version:** 1.0.0

## Authentication
Sebagian besar endpoint memerlukan autentikasi JWT token yang diperoleh dari Auth Service.

**Header Required:**
```
Authorization: Bearer <jwt_token>
```

## Rate Limiting
- **General Gateway:** 5000 requests per 15 minutes
- **Auth Endpoints:** 100 requests per 15 minutes
- **Assessment Endpoints:** 100 requests per 15 minutes
- **Admin Endpoints:** 50 requests per 15 minutes
- **Archive Endpoints:** 5000 requests per 15 minutes
- **Chat Endpoints:** 500 requests per 15 minutes

---

## 🔐 Authentication Service Routes (`/api/auth/`)

### Public Endpoints (No Authentication)

#### POST /api/auth/register
Mendaftarkan user baru ke sistem.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1",
  "username": "johndoe"
}
```

**Response Success (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "User registered successfully"
}
```

#### POST /api/auth/login
Login user ke sistem.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "myPassword1"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "email": "<EMAIL>",
      "username": "johndoe",
      "user_type": "user",
      "is_active": true,
      "token_balance": 5
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "Login successful"
}
```

### Protected Endpoints (Authentication Required)

#### GET /api/auth/profile
Mendapatkan profil user yang sedang login.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "email": "<EMAIL>",
    "username": "johndoe",
    "user_type": "user",
    "is_active": true,
    "token_balance": 5,
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

#### PUT /api/auth/profile
Update profil user.

**Request Body:**
```json
{
  "username": "newusername",
  "full_name": "John Doe Updated"
}
```

#### POST /api/auth/change-password
Mengubah password user.

**Request Body:**
```json
{
  "currentPassword": "oldPassword1",
  "newPassword": "newPassword1"
}
```

#### POST /api/auth/logout
Logout user dari sistem.

#### GET /api/auth/token-balance
Mendapatkan saldo token user.

#### GET /api/auth/schools
Mendapatkan daftar sekolah.

#### POST /api/auth/schools
Membuat sekolah baru.

**Request Body:**
```json
{
  "name": "SMA Negeri 1 Jakarta",
  "address": "Jl. Sudirman No. 1",
  "city": "Jakarta",
  "province": "DKI Jakarta"
}
```

---

## 🎯 Assessment Service Routes (`/api/assessment/`)

### Assessment Submission

#### POST /api/assessment/submit
Submit assessment data untuk analisis AI.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Idempotency-Key: <unique_key> (optional)
```

**Request Body:**
```json
{
  "assessmentName": "AI-Driven Talent Mapping",
  "riasec": {
    "realistic": 85,
    "investigative": 92,
    "artistic": 78,
    "social": 65,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 88,
    "conscientiousness": 75,
    "extraversion": 82,
    "agreeableness": 90,
    "neuroticism": 45
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 90,
    "judgment": 78
  }
}
```

**Response Success (202):**
```json
{
  "success": true,
  "data": {
    "jobId": "job_550e8400-e29b-41d4-a716-446655440000",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 1
  },
  "message": "Assessment submitted successfully"
}
```

#### GET /api/assessment/status/:jobId
Mengecek status assessment yang sedang diproses.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "jobId": "job_550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "progress": 100,
    "resultId": "result_550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### Health Endpoints

#### GET /api/assessment/health
Health check assessment service.

#### GET /api/assessment/health/ready
Readiness probe.

#### GET /api/assessment/health/live
Liveness probe.

#### GET /api/assessment/health/queue
Queue status check.

---

## 📁 Archive Service Routes (`/api/archive/`)

### Results Management

#### GET /api/archive/results
Mendapatkan daftar hasil assessment user.

**Query Parameters:**
- `page` (number): Halaman (default: 1)
- `limit` (number): Jumlah per halaman (default: 10)
- `status` (string): Filter status (completed, failed)

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "result_550e8400-e29b-41d4-a716-446655440000",
        "assessment_name": "AI-Driven Talent Mapping",
        "status": "completed",
        "created_at": "2024-01-15T10:30:00.000Z",
        "analysis_summary": {
          "dominant_archetype": "The Innovator",
          "career_recommendations": ["Software Engineer", "Data Scientist"]
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

#### GET /api/archive/results/:resultId
Mendapatkan detail hasil assessment.

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "id": "result_550e8400-e29b-41d4-a716-446655440000",
    "assessment_name": "AI-Driven Talent Mapping",
    "status": "completed",
    "analysis_summary": {
      "dominant_archetype": "The Innovator",
      "personality_insights": "...",
      "career_recommendations": ["Software Engineer", "Data Scientist"],
      "strengths": ["Problem Solving", "Analytical Thinking"],
      "development_areas": ["Communication", "Leadership"]
    },
    "raw_analysis": "...",
    "created_at": "2024-01-15T10:30:00.000Z"
  }
}
```

### Job Tracking

#### GET /api/archive/jobs
Mendapatkan daftar job assessment user.

#### GET /api/archive/jobs/:jobId
Mendapatkan detail job assessment.

### Statistics

#### GET /api/archive/v1/stats
Endpoint statistik terpadu.

**Query Parameters:**
- `type` (string): user, system, demographic, performance
- `scope` (string): overview, detailed, analysis, summary
- `timeRange` (string): "1 day", "7 days", "30 days", "90 days"
